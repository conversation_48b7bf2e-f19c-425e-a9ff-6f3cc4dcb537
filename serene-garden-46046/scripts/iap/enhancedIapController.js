/* eslint-disable class-methods-use-this */
/* eslint-disable max-statements */
const iapService = require("../services/iapService");
const UserController = require("../DB/schemes/user/user.ctrl");
const IAPErrorHandler = require("../middleware/errorHandler");
const RetryManager = require("../utils/retry");
const { circuitBreakerManager } = require("../utils/circuitBreakerManager");
const HealthCheckManager = require("../utils/healthCheckManager");

class EnhancedIAPController {
    constructor() {
        this.healthCheckManager = new HealthCheckManager();
        this.initializeHealthChecks();
    }

    /**
     * Initialize health checks for IAP services
     */
    initializeHealthChecks() {
        // Register health checks will be done in a separate initialization
        console.log("Enhanced IAP Controller initialized with health monitoring");
    }

    /**
     * Verify a purchase receipt with enhanced error handling
     * POST /api/iap/verify-purchase
     */
    verifyPurchase(req, res) {
        const errorHandler = IAPErrorHandler.asyncHandler(async (request, response) => {
            const { platform, productId, receipt, purchaseToken, deviceId, channelId } = request.body;
            console.log("verifyPurchase:", platform, productId, deviceId, channelId);
            // Validate required fields
            this.validatePurchaseRequest(request.body);

            // Find or create user with retry mechanism
            const user = await this.findUserWithRetry(deviceId, channelId);
            if (!user) {
                return response.status(404).json({
                    success: false,
                    error: "User not found"
                });
            }

            // Verify the purchase with circuit breaker protection
            const verificationResult = await this.verifyPurchaseWithProtection({
                platform,
                productId,
                receipt,
                purchaseToken,
                userId: user._id,
                deviceId
            });

            if (!verificationResult.success) {
                return response.status(400).json({
                    success: false,
                    error: verificationResult.error,
                    errorCode: verificationResult.errorCode
                });
            }

            // Return success with purchase and user status
            response.json({
                success: true,
                message: "Purchase verified successfully",
                purchase: {
                    transactionId: verificationResult.purchase.transactionId,
                    productId: verificationResult.purchase.productId,
                    status: verificationResult.purchase.status,
                    expiryDate: verificationResult.purchase.expiryDate
                },
                userStatus: verificationResult.userStatus
            });
        });

        return errorHandler(req, res);
    }

    /**
     * Validate purchase request parameters
     */
    validatePurchaseRequest(body) {
        const { platform, productId, receipt, deviceId, channelId, purchaseToken } = body;

        if (!platform || !productId || !receipt || !deviceId || !channelId) {
            const error = new Error("Missing required fields: platform, productId, receipt, deviceId, channelId");
            error.name = "ValidationError";
            throw error;
        }

        if (platform === "android" && !purchaseToken) {
            const error = new Error("purchaseToken is required for Android purchases");
            error.name = "ValidationError";
            throw error;
        }
    }

    /**
     * Find user with retry mechanism
     */
    async findUserWithRetry(deviceId, channelId) {
        return RetryManager.forDatabase(async () => {
            return UserController.find({ deviceId, channelId });
        });
    }

    /**
     * Verify purchase with circuit breaker protection
     */
    async verifyPurchaseWithProtection(receiptData) {
        const serviceName = `iap-verification-${receiptData.platform}`;

        return circuitBreakerManager.execute(serviceName, async () => {
            return iapService.verifyPurchase(receiptData);
        }, {
            failureThreshold: 3,
            resetTimeout: 30000 // 30 seconds
        });
    }

    /**
     * Get user's current IAP status with enhanced error handling
     * GET /api/iap/status/:deviceId/:channelId
     */
    async getUserStatus(req, res) {
        const errorHandler = IAPErrorHandler.asyncHandler(async (request, response) => {
            const { deviceId, channelId } = request.params;

            if (!deviceId || !channelId) {
                return response.status(400).json({
                    success: false,
                    error: "deviceId and channelId are required"
                });
            }

            // Find user with retry
            const user = await this.findUserWithRetry(deviceId, channelId);
            if (!user) {
                return response.status(404).json({
                    success: false,
                    error: "User not found"
                });
            }

            // Get IAP status with circuit breaker protection
            const iapStatus = await circuitBreakerManager.execute("iap-status", async () => {
                return iapService.getUserIAPStatus(user._id);
            });

            response.json({
                success: true,
                status: iapStatus
            });
        });

        return errorHandler(req, res);
    }

    /**
     * Refresh user's IAP status with enhanced error handling
     * POST /api/iap/refresh-status
     */
    async refreshUserStatus(req, res) {
        const errorHandler = IAPErrorHandler.asyncHandler(async (request, response) => {
            const { deviceId, channelId } = request.body;

            if (!deviceId || !channelId) {
                return response.status(400).json({
                    success: false,
                    error: "deviceId and channelId are required"
                });
            }

            // Find user with retry
            const user = await this.findUserWithRetry(deviceId, channelId);
            if (!user) {
                return response.status(404).json({
                    success: false,
                    error: "User not found"
                });
            }

            // Update user's IAP status with circuit breaker protection
            const updatedStatus = await circuitBreakerManager.execute("iap-refresh", async () => {
                return iapService.updateUserIAPStatus(user._id);
            });

            response.json({
                success: true,
                message: "Status refreshed successfully",
                status: updatedStatus
            });
        });

        return errorHandler(req, res);
    }

    /**
     * Handle Apple App Store Server-to-Server notifications with enhanced error handling
     * POST /api/iap/webhook/apple
     */
    async handleAppleWebhook(req, res) {
        const errorHandler = IAPErrorHandler.asyncHandler(async (request, response) => {
            console.log("Received Apple webhook notification");

            const notificationPayload = request.body;

            // Process the notification with circuit breaker protection
            const result = await circuitBreakerManager.execute("apple-webhook", async () => {
                return iapService.handleStoreNotification("ios", notificationPayload);
            }, {
                failureThreshold: 2,
                resetTimeout: 60000 // 1 minute
            });

            if (result.success) {
                response.status(200).json({ status: "ok" });
            } else {
                console.error("Failed to process Apple notification:", result.error);
                response.status(400).json({ error: result.error });
            }
        });

        return errorHandler(req, res);
    }

    /**
     * Handle Google Play Developer Notifications with enhanced error handling
     * POST /api/iap/webhook/google
     */
    async handleGoogleWebhook(req, res) {
        const errorHandler = IAPErrorHandler.asyncHandler(async (request, response) => {
            console.log("Received Google Play webhook notification");

            const notificationPayload = request.body;

            // Process the notification with circuit breaker protection
            const result = await circuitBreakerManager.execute("google-webhook", async () => {
                return iapService.handleStoreNotification("android", notificationPayload);
            }, {
                failureThreshold: 2,
                resetTimeout: 60000 // 1 minute
            });

            if (result.success) {
                response.status(200).json({ status: "ok" });
            } else {
                console.error("Failed to process Google notification:", result.error);
                response.status(400).json({ error: result.error });
            }
        });

        return errorHandler(req, res);
    }

    /**
     * Get purchase history with enhanced error handling
     * GET /api/iap/purchases/:deviceId/:channelId
     */
    async getPurchaseHistory(req, res) {
        const errorHandler = IAPErrorHandler.asyncHandler(async (request, response) => {
            const { deviceId, channelId } = request.params;
            const { limit = 10, offset = 0 } = request.query;

            if (!deviceId || !channelId) {
                return response.status(400).json({
                    success: false,
                    error: "deviceId and channelId are required"
                });
            }

            // Find user with retry
            const user = await this.findUserWithRetry(deviceId, channelId);
            if (!user) {
                return response.status(404).json({
                    success: false,
                    error: "User not found"
                });
            }

            // Get purchase history with database retry
            const { purchases, total } = await RetryManager.forDatabase(async () => {
                const Purchase = require("../DB/schemes/iap/purchase");
                const purchaseList = await Purchase.find({ user: user._id })
                    .sort({ createdAt: -1 })
                    .limit(parseInt(limit, 10))
                    .skip(parseInt(offset, 10))
                    .select("transactionId productId status purchaseDate expiryDate platform");

                const totalCount = await Purchase.countDocuments({ user: user._id });

                return { purchases: purchaseList, total: totalCount };
            });

            response.json({
                success: true,
                purchases,
                pagination: {
                    limit: parseInt(limit, 10),
                    offset: parseInt(offset, 10),
                    total
                }
            });
        });

        return errorHandler(req, res);
    }

    /**
     * Enhanced health check endpoint for IAP service
     * GET /api/iap/health
     */
    async healthCheck(req, res) {
        const errorHandler = IAPErrorHandler.asyncHandler(async (request, response) => {
            const healthStatus = await this.healthCheckManager.getHealthStatus();

            const statusCode = healthStatus.status === "healthy" ? 200 :
                healthStatus.status === "degraded" ? 200 : 503;

            response.status(statusCode).json({
                success: healthStatus.status !== "unhealthy",
                ...healthStatus,
                version: "2.0.0"
            });
        });

        return errorHandler(req, res);
    }

    /**
     * Get circuit breaker status
     * GET /api/iap/circuit-breakers
     */
    async getCircuitBreakerStatus(req, res) {
        const errorHandler = IAPErrorHandler.asyncHandler(async (request, response) => {
            const status = circuitBreakerManager.getAllStatus();
            const summary = circuitBreakerManager.getHealthSummary();

            response.json({
                success: true,
                summary,
                circuitBreakers: status,
                timestamp: new Date().toISOString()
            });
        });

        return errorHandler(req, res);
    }
}

module.exports = new EnhancedIAPController();