import { useEffect, useRef } from 'react';
import { AppState } from 'react-native';
import { AppOpenAd, AdEventType } from 'react-native-google-mobile-ads';
import remoteConfig from '@react-native-firebase/remote-config';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getAdFreeStatus } from '../../contexts/IAPContext';

let retryAttempt = 0;
let appOpenAd;
remoteConfig()
  .ensureInitialized()
  .finally(
    () =>
    (appOpenAd = AppOpenAd.createForAdRequest(
      remoteConfig().getValue('AppOpenAdId').asString(),
    )),
  );

export function useAppOpen(showAds) {
  const appState = useRef(AppState.currentState);
  const isAdFree = getAdFreeStatus(); // Use optimized cached ad-free check
  console.log('isAdFree', isAdFree);

  useEffect(() => {
    const checkAdsPreference = async () => {
      try {
        if (!isAdFree) {
          console.log('No valid ad-free status found, showing ads');
          initializeAd();
        }
      } catch (error) {
        console.error('Error checking ads preference:', error);
        // On error, default to showing ads
        initializeAd();
      }
    };

    const initializeAd = () => {
      if (appOpenAd && showAds) {
        console.log('Initializing app open ad');
        try {
          if (!appOpenAd.loaded) {
            appOpenAd.addAdEventListener(AdEventType.ERROR, error => {
              try {
                retryAttempt += 1;
                var retryDelay = Math.pow(2, Math.min(6, retryAttempt));
                setTimeout(function () {
                  appOpenAd.load();
                }, retryDelay * 1000);
              } catch (err) {
                console.log('Error handling ad error:', err);
              }
            });

            appOpenAd.addAdEventListener(AdEventType.CLOSED, () => {
              try {
                appOpenAd.load();
              } catch (error) {
                console.log('Error reloading ad after close:', error);
              }
            });
            appOpenAd.load();
          }
        } catch (error) {
          console.error('Error initializing ad:', error);
        }
      }
    };

    // Check subscription status on mount
    checkAdsPreference();

    const subscription = AppState.addEventListener('change', nextAppState => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        // Revalidate subscription status when app comes to foreground
        checkAdsPreference().then(() => {
          if (appOpenAd && appOpenAd.loaded && !isAdFree) {
            showAdIfReady();
          }
        });
      }

      appState.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, [appOpenAd, showAds]);

  const showAdIfReady = () => {
    try {
      if (appOpenAd) {
        if (appOpenAd.loaded) {
          appOpenAd.show();
        } else {
          appOpenAd.load();
        }
      }
    } catch (error) {
      console.log('Error showing ad:', error);
    }
  };
}
